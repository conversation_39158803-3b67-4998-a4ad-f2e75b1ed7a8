{"runtimeTarget": {"name": ".NETFramework,Version=v4.6.2/win-x86", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "RELEASE", "NETFRAMEWORK", "NET462", "NET20_OR_GREATER", "NET30_OR_GREATER", "NET35_OR_GREATER", "NET40_OR_GREATER", "NET45_OR_GREATER", "NET451_OR_GREATER", "NET452_OR_GREATER", "NET46_OR_GREATER", "NET461_OR_GREATER", "NET462_OR_GREATER"], "languageVersion": "7.3", "platform": "AnyCPU", "allowUnsafe": false, "warningsAsErrors": false, "optimize": true, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETFramework,Version=v4.6.2": {"WeatherDataImporter/1.0.0": {"dependencies": {"Grassfish.UDC.PluginBase": "1.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Swashbuckle.AspNetCore": "6.6.2", "System.ComponentModel": "4.3.0", "System.ComponentModel.Composition": "9.0.2", "System.Composition": "9.0.2", "WeathertImporter": "1.0.0", "mscorlib": "*******", "System.ComponentModel.Composition.Reference": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.Core": "*******", "System.Data": "*******", "System": "*******", "System.Drawing": "*******", "System.IO.Compression.FileSystem": "*******", "System.Numerics": "*******", "System.Runtime.Serialization": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Reflection.Emit": "0.0.0.0", "System.Reflection.Emit.ILGeneration": "0.0.0.0", "System.Reflection.Emit.Lightweight": "0.0.0.0", "System.Runtime.InteropServices.WindowsRuntime": "0.0.0.0", "System.ServiceModel.Duplex": "0.0.0.0", "System.ServiceModel.Http": "0.0.0.0", "System.ServiceModel.NetTcp": "0.0.0.0", "System.ServiceModel.Primitives": "0.0.0.0", "System.ServiceModel.Security": "0.0.0.0", "Microsoft.Win32.Primitives": "*******", "netfx.force.conflicts": "0.0.0.0", "netstandard": "2.0.0.0", "System.AppContext": "*******", "System.Collections.Concurrent": "********", "System.Collections": "********", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Reference": "*******", "System.ComponentModel.EventBasedAsync": "********", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Console": "*******", "System.Data.Common": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug": "********", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "********", "System.Globalization.Calendars": "*******", "System.Globalization": "********", "System.Globalization.Extensions": "*******", "System.IO.Compression": "*******", "System.IO.Compression.ZipFile": "*******", "System.IO": "*******", "System.IO.FileSystem": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq": "*******", "System.Linq.Expressions": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Net.Http": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives": "********", "System.Net.Requests": "********", "System.Net.Security": "*******", "System.Net.Sockets": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.ObjectModel": "********", "System.Reflection": "*******", "System.Reflection.Extensions": "*******", "System.Reflection.Primitives": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime": "*******", "System.Runtime.Extensions": "*******", "System.Runtime.Handles": "*******", "System.Runtime.InteropServices": "*******", "System.Runtime.InteropServices.RuntimeInformation.Reference": "*******", "System.Runtime.Numerics": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.Claims": "*******", "System.Security.Cryptography.Algorithms": "*******", "System.Security.Cryptography.Csp": "*******", "System.Security.Cryptography.Encoding": "*******", "System.Security.Cryptography.Primitives": "*******", "System.Security.Cryptography.X509Certificates": "*******", "System.Security.Principal": "*******", "System.Security.SecureString": "*******", "System.Text.Encoding": "********", "System.Text.Encoding.Extensions": "********", "System.Text.RegularExpressions": "*******", "System.Threading": "********", "System.Threading.Overlapped": "*******", "System.Threading.Tasks": "********", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "********", "System.Threading.Timer": "*******", "System.Xml.ReaderWriter": "*******", "System.Xml.XDocument": "********", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "********", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******"}, "compile": {"WeatherDataImporter.exe": {}}}, "mscorlib/*******": {"compile": {".NETFramework/v4.6.2/mscorlib.dll": {}}}, "System.ComponentModel.Composition.Reference/*******": {"compile": {".NETFramework/v4.6.2/System.ComponentModel.Composition.dll": {}}}, "System.ComponentModel.DataAnnotations/*******": {"compile": {".NETFramework/v4.6.2/System.ComponentModel.DataAnnotations.dll": {}}}, "System.Core/*******": {"compile": {".NETFramework/v4.6.2/System.Core.dll": {}}}, "System.Data/*******": {"compile": {".NETFramework/v4.6.2/System.Data.dll": {}}}, "System/*******": {"compile": {".NETFramework/v4.6.2/System.dll": {}}}, "System.Drawing/*******": {"compile": {".NETFramework/v4.6.2/System.Drawing.dll": {}}}, "System.IO.Compression.FileSystem/*******": {"compile": {".NETFramework/v4.6.2/System.IO.Compression.FileSystem.dll": {}}}, "System.Numerics/*******": {"compile": {".NETFramework/v4.6.2/System.Numerics.dll": {}}}, "System.Runtime.Serialization/*******": {"compile": {".NETFramework/v4.6.2/System.Runtime.Serialization.dll": {}}}, "System.Xml/*******": {"compile": {".NETFramework/v4.6.2/System.Xml.dll": {}}}, "System.Xml.Linq/*******": {"compile": {".NETFramework/v4.6.2/System.Xml.Linq.dll": {}}}, "System.Reflection.Emit/0.0.0.0": {"compile": {".NETFramework/v4.6.2/Facades/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/0.0.0.0": {"compile": {".NETFramework/v4.6.2/Facades/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/0.0.0.0": {"compile": {".NETFramework/v4.6.2/Facades/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Runtime.InteropServices.WindowsRuntime/0.0.0.0": {"compile": {".NETFramework/v4.6.2/Facades/System.Runtime.InteropServices.WindowsRuntime.dll": {}}}, "System.ServiceModel.Duplex/0.0.0.0": {"compile": {".NETFramework/v4.6.2/Facades/System.ServiceModel.Duplex.dll": {}}}, "System.ServiceModel.Http/0.0.0.0": {"compile": {".NETFramework/v4.6.2/Facades/System.ServiceModel.Http.dll": {}}}, "System.ServiceModel.NetTcp/0.0.0.0": {"compile": {".NETFramework/v4.6.2/Facades/System.ServiceModel.NetTcp.dll": {}}}, "System.ServiceModel.Primitives/0.0.0.0": {"compile": {".NETFramework/v4.6.2/Facades/System.ServiceModel.Primitives.dll": {}}}, "System.ServiceModel.Security/0.0.0.0": {"compile": {".NETFramework/v4.6.2/Facades/System.ServiceModel.Security.dll": {}}}, "Microsoft.Win32.Primitives/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}}, "netfx.force.conflicts/0.0.0.0": {"compile": {"netfx.force.conflicts.dll": {}}}, "netstandard/2.0.0.0": {"compile": {"netstandard.dll": {}}}, "System.AppContext/*******": {"compile": {"System.AppContext.dll": {}}}, "System.Collections.Concurrent/********": {"compile": {"System.Collections.Concurrent.dll": {}}}, "System.Collections/********": {"compile": {"System.Collections.dll": {}}}, "System.Collections.NonGeneric/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/*******": {"compile": {"System.Collections.Specialized.dll": {}}}, "System.ComponentModel.Reference/*******": {"compile": {"System.ComponentModel.dll": {}}}, "System.ComponentModel.EventBasedAsync/********": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}}, "System.ComponentModel.Primitives/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}}, "System.Console/*******": {"compile": {"System.Console.dll": {}}}, "System.Data.Common/*******": {"compile": {"System.Data.Common.dll": {}}}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}}, "System.Diagnostics.Debug/********": {"compile": {"System.Diagnostics.Debug.dll": {}}}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}}, "System.Diagnostics.Tools/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}}, "System.Diagnostics.Tracing/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}}, "System.Dynamic.Runtime/********": {"compile": {"System.Dynamic.Runtime.dll": {}}}, "System.Globalization.Calendars/*******": {"compile": {"System.Globalization.Calendars.dll": {}}}, "System.Globalization/********": {"compile": {"System.Globalization.dll": {}}}, "System.Globalization.Extensions/*******": {"compile": {"System.Globalization.Extensions.dll": {}}}, "System.IO.Compression/*******": {"compile": {"System.IO.Compression.dll": {}}}, "System.IO.Compression.ZipFile/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}}, "System.IO/*******": {"compile": {"System.IO.dll": {}}}, "System.IO.FileSystem/*******": {"compile": {"System.IO.FileSystem.dll": {}}}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}}, "System.IO.FileSystem.Primitives/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}}, "System.Linq/*******": {"compile": {"System.Linq.dll": {}}}, "System.Linq.Expressions/*******": {"compile": {"System.Linq.Expressions.dll": {}}}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}}, "System.Linq.Queryable/*******": {"compile": {"System.Linq.Queryable.dll": {}}}, "System.Net.Http/*******": {"compile": {"System.Net.Http.dll": {}}}, "System.Net.NameResolution/*******": {"compile": {"System.Net.NameResolution.dll": {}}}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}}, "System.Net.Primitives/********": {"compile": {"System.Net.Primitives.dll": {}}}, "System.Net.Requests/********": {"compile": {"System.Net.Requests.dll": {}}}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}}, "System.Net.Sockets/*******": {"compile": {"System.Net.Sockets.dll": {}}}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}}, "System.Net.WebSockets/*******": {"compile": {"System.Net.WebSockets.dll": {}}}, "System.ObjectModel/********": {"compile": {"System.ObjectModel.dll": {}}}, "System.Reflection/*******": {"compile": {"System.Reflection.dll": {}}}, "System.Reflection.Extensions/*******": {"compile": {"System.Reflection.Extensions.dll": {}}}, "System.Reflection.Primitives/*******": {"compile": {"System.Reflection.Primitives.dll": {}}}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}}, "System.Resources.ResourceManager/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}}, "System.Runtime/*******": {"compile": {"System.Runtime.dll": {}}}, "System.Runtime.Extensions/*******": {"compile": {"System.Runtime.Extensions.dll": {}}}, "System.Runtime.Handles/*******": {"compile": {"System.Runtime.Handles.dll": {}}}, "System.Runtime.InteropServices/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation.Reference/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}}, "System.Runtime.Numerics/*******": {"compile": {"System.Runtime.Numerics.dll": {}}}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}}, "System.Runtime.Serialization.Json/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}}, "System.Runtime.Serialization.Primitives/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}}, "System.Runtime.Serialization.Xml/*******": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}}, "System.Security.Claims/*******": {"compile": {"System.Security.Claims.dll": {}}}, "System.Security.Cryptography.Algorithms/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}}, "System.Security.Cryptography.Csp/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}}, "System.Security.Cryptography.Encoding/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}}, "System.Security.Cryptography.Primitives/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.X509Certificates/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}}, "System.Security.Principal/*******": {"compile": {"System.Security.Principal.dll": {}}}, "System.Security.SecureString/*******": {"compile": {"System.Security.SecureString.dll": {}}}, "System.Text.Encoding/********": {"compile": {"System.Text.Encoding.dll": {}}}, "System.Text.Encoding.Extensions/********": {"compile": {"System.Text.Encoding.Extensions.dll": {}}}, "System.Text.RegularExpressions/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}}, "System.Threading/********": {"compile": {"System.Threading.dll": {}}}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}}, "System.Threading.Tasks/********": {"compile": {"System.Threading.Tasks.dll": {}}}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}}, "System.Threading.Thread/*******": {"compile": {"System.Threading.Thread.dll": {}}}, "System.Threading.ThreadPool/********": {"compile": {"System.Threading.ThreadPool.dll": {}}}, "System.Threading.Timer/*******": {"compile": {"System.Threading.Timer.dll": {}}}, "System.Xml.ReaderWriter/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}}, "System.Xml.XDocument/********": {"compile": {"System.Xml.XDocument.dll": {}}}, "System.Xml.XmlDocument/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}}, "System.Xml.XmlSerializer/********": {"compile": {"System.Xml.XmlSerializer.dll": {}}}, "System.Xml.XPath/*******": {"compile": {"System.Xml.XPath.dll": {}}}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}}, "Grassfish.UDC.PluginBase/1.0.3": {"compile": {"lib/net45/UdcPluginBase.dll": {}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Authentication.Core/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {}}}, "Microsoft.AspNetCore.Authorization/2.1.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {}}}, "Microsoft.AspNetCore.Authorization.Policy/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.0", "Microsoft.AspNetCore.Authorization": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.Hosting.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "Microsoft.Extensions.Configuration.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.AspNetCore.WebUtilities": "2.1.0", "Microsoft.Extensions.ObjectPool": "2.1.0", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Net.Http.Headers": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {}}}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "System.Text.Encodings.Web": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {}}}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}}, "Microsoft.AspNetCore.Mvc.Core/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.0", "Microsoft.AspNetCore.Authorization.Policy": "2.1.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.1.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.1.0", "Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.0", "Microsoft.Extensions.Localization": "2.1.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Routing/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.ObjectPool": "2.1.0", "Microsoft.Extensions.Options": "9.0.3"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {}}}, "Microsoft.AspNetCore.StaticFiles/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.WebEncoders": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/2.1.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.1.0", "System.Text.Encodings.Web": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "compile": {"lib/net45/Microsoft.DotNet.PlatformAbstractions.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Configuration.Abstractions/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "9.0.1"}, "compile": {"lib/net451/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}}, "Microsoft.Extensions.FileProviders.Embedded/2.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {}}}, "Microsoft.Extensions.Hosting.Abstractions/2.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {}}}, "Microsoft.Extensions.Localization/2.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Localization.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {}}}, "Microsoft.Extensions.Localization.Abstractions/2.1.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging/9.0.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.Memory": "4.5.5"}, "compile": {"lib/net462/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.ObjectPool/2.1.0": {"compile": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {}}}, "Microsoft.Extensions.Options/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Primitives/9.0.3": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.WebEncoders/2.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Text.Encodings.Web": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {}}}, "Microsoft.Net.Http.Headers/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {}}}, "Microsoft.OpenApi/1.6.14": {"compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Newtonsoft.Json/9.0.1": {"compile": {"lib/net45/Newtonsoft.Json.dll": {}}}, "Swashbuckle.AspNetCore/6.6.2": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.6.2", "Swashbuckle.AspNetCore.SwaggerGen": "6.6.2", "Swashbuckle.AspNetCore.SwaggerUI": "6.6.2"}}, "Swashbuckle.AspNetCore.Swagger/6.6.2": {"dependencies": {"Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.OpenApi": "1.6.14"}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.6.2": {"dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.1.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.0", "Swashbuckle.AspNetCore.Swagger": "6.6.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.6.2": {"dependencies": {"Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.AspNetCore.StaticFiles": "2.1.0", "Microsoft.Extensions.FileProviders.Embedded": "2.1.0", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.Buffers/4.5.1": {"compile": {"ref/net45/System.Buffers.dll": {}}}, "System.ComponentModel/4.3.0": {}, "System.ComponentModel.Annotations/4.5.0": {"compile": {"ref/net461/System.ComponentModel.Annotations.dll": {}}}, "System.ComponentModel.Composition/9.0.2": {}, "System.Composition/9.0.2": {"dependencies": {"System.Composition.AttributedModel": "9.0.2", "System.Composition.Convention": "9.0.2", "System.Composition.Hosting": "9.0.2", "System.Composition.Runtime": "9.0.2", "System.Composition.TypedParts": "9.0.2"}}, "System.Composition.AttributedModel/9.0.2": {"compile": {"lib/net462/System.Composition.AttributedModel.dll": {}}}, "System.Composition.Convention/9.0.2": {"dependencies": {"System.Composition.AttributedModel": "9.0.2"}, "compile": {"lib/net462/System.Composition.Convention.dll": {}}}, "System.Composition.Hosting/9.0.2": {"dependencies": {"System.Composition.Runtime": "9.0.2"}, "compile": {"lib/net462/System.Composition.Hosting.dll": {}}}, "System.Composition.Runtime/9.0.2": {"compile": {"lib/net462/System.Composition.Runtime.dll": {}}}, "System.Composition.TypedParts/9.0.2": {"dependencies": {"System.Composition.AttributedModel": "9.0.2", "System.Composition.Hosting": "9.0.2", "System.Composition.Runtime": "9.0.2"}, "compile": {"lib/net462/System.Composition.TypedParts.dll": {}}}, "System.Diagnostics.DiagnosticSource/9.0.3": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Memory/4.5.5": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net461/System.Memory.dll": {}}}, "System.Numerics.Vectors/4.5.0": {"compile": {"ref/net46/System.Numerics.Vectors.dll": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {}, "System.Text.Encodings.Web/4.6.0": {"dependencies": {"System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {}}}, "System.Text.Json/4.6.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net461/System.Text.Json.dll": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {}}}, "System.ValueTuple/4.5.0": {"compile": {"ref/net461/System.ValueTuple.dll": {}}}, "WeathertImporter/1.0.0": {"dependencies": {"Grassfish.UDC.PluginBase": "1.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "System.ComponentModel.Composition": "9.0.2"}, "compile": {"WeathertImporter.dll": {}}}}, ".NETFramework,Version=v4.6.2/win-x86": {"WeatherDataImporter/1.0.0": {"dependencies": {"Grassfish.UDC.PluginBase": "1.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Swashbuckle.AspNetCore": "6.6.2", "System.ComponentModel": "4.3.0", "System.ComponentModel.Composition": "9.0.2", "System.Composition": "9.0.2", "WeathertImporter": "1.0.0", "mscorlib": "*******", "System.ComponentModel.Composition.Reference": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.Core": "*******", "System.Data": "*******", "System": "*******", "System.Drawing": "*******", "System.IO.Compression.FileSystem": "*******", "System.Numerics": "*******", "System.Runtime.Serialization": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Reflection.Emit": "0.0.0.0", "System.Reflection.Emit.ILGeneration": "0.0.0.0", "System.Reflection.Emit.Lightweight": "0.0.0.0", "System.Runtime.InteropServices.WindowsRuntime": "0.0.0.0", "System.ServiceModel.Duplex": "0.0.0.0", "System.ServiceModel.Http": "0.0.0.0", "System.ServiceModel.NetTcp": "0.0.0.0", "System.ServiceModel.Primitives": "0.0.0.0", "System.ServiceModel.Security": "0.0.0.0", "Microsoft.Win32.Primitives": "*******", "netfx.force.conflicts": "0.0.0.0", "netstandard": "2.0.0.0", "System.AppContext": "*******", "System.Collections.Concurrent": "********", "System.Collections": "********", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Reference": "*******", "System.ComponentModel.EventBasedAsync": "********", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Console": "*******", "System.Data.Common": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug": "********", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "********", "System.Globalization.Calendars": "*******", "System.Globalization": "********", "System.Globalization.Extensions": "*******", "System.IO.Compression": "*******", "System.IO.Compression.ZipFile": "*******", "System.IO": "*******", "System.IO.FileSystem": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq": "*******", "System.Linq.Expressions": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Net.Http": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives": "********", "System.Net.Requests": "********", "System.Net.Security": "*******", "System.Net.Sockets": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.ObjectModel": "********", "System.Reflection": "*******", "System.Reflection.Extensions": "*******", "System.Reflection.Primitives": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime": "*******", "System.Runtime.Extensions": "*******", "System.Runtime.Handles": "*******", "System.Runtime.InteropServices": "*******", "System.Runtime.InteropServices.RuntimeInformation.Reference": "*******", "System.Runtime.Numerics": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.Claims": "*******", "System.Security.Cryptography.Algorithms": "*******", "System.Security.Cryptography.Csp": "*******", "System.Security.Cryptography.Encoding": "*******", "System.Security.Cryptography.Primitives": "*******", "System.Security.Cryptography.X509Certificates": "*******", "System.Security.Principal": "*******", "System.Security.SecureString": "*******", "System.Text.Encoding": "********", "System.Text.Encoding.Extensions": "********", "System.Text.RegularExpressions": "*******", "System.Threading": "********", "System.Threading.Overlapped": "*******", "System.Threading.Tasks": "********", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "********", "System.Threading.Timer": "*******", "System.Xml.ReaderWriter": "*******", "System.Xml.XDocument": "********", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "********", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******"}, "runtime": {"WeatherDataImporter.exe": {}}}, "Grassfish.UDC.PluginBase/1.0.3": {"runtime": {"lib/net45/UdcPluginBase.dll": {}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Authentication.Core/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {}}}, "Microsoft.AspNetCore.Authorization/2.1.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {}}}, "Microsoft.AspNetCore.Authorization.Policy/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.1.0", "Microsoft.AspNetCore.Authorization": "2.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.Hosting.Abstractions": "2.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "Microsoft.Extensions.Configuration.Abstractions": "2.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.AspNetCore.WebUtilities": "2.1.0", "Microsoft.Extensions.ObjectPool": "2.1.0", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Net.Http.Headers": "2.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {}}}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.0", "System.Text.Encodings.Web": "4.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {}}}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.1.0", "Microsoft.Net.Http.Headers": "2.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}}, "Microsoft.AspNetCore.Mvc.Core/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.1.0", "Microsoft.AspNetCore.Authorization.Policy": "2.1.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.1.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.1.0", "Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.0", "Microsoft.Extensions.Localization": "2.1.0", "System.ComponentModel.Annotations": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Routing/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.ObjectPool": "2.1.0", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll": {}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {}}}, "Microsoft.AspNetCore.StaticFiles/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.WebEncoders": "2.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.StaticFiles.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/2.1.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.1.0", "System.Text.Encodings.Web": "4.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "runtime": {"lib/net45/Microsoft.DotNet.PlatformAbstractions.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Configuration.Abstractions/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net462/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "9.0.1"}, "runtime": {"lib/net451/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}}, "Microsoft.Extensions.FileProviders.Embedded/2.1.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll": {}}}, "Microsoft.Extensions.Hosting.Abstractions/2.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {}}}, "Microsoft.Extensions.Localization/2.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Localization.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.dll": {}}}, "Microsoft.Extensions.Localization.Abstractions/2.1.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging/9.0.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/net462/Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.Memory": "4.5.5"}, "runtime": {"lib/net462/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.ObjectPool/2.1.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {}}}, "Microsoft.Extensions.Options/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/net462/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Primitives/9.0.3": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net462/Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.WebEncoders/2.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Text.Encodings.Web": "4.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.WebEncoders.dll": {}}}, "Microsoft.Net.Http.Headers/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {}}}, "Microsoft.OpenApi/1.6.14": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Newtonsoft.Json/9.0.1": {"runtime": {"lib/net45/Newtonsoft.Json.dll": {}}}, "Swashbuckle.AspNetCore/6.6.2": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.6.2", "Swashbuckle.AspNetCore.SwaggerGen": "6.6.2", "Swashbuckle.AspNetCore.SwaggerUI": "6.6.2"}}, "Swashbuckle.AspNetCore.Swagger/6.6.2": {"dependencies": {"Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.6.2": {"dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.1.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.0", "Swashbuckle.AspNetCore.Swagger": "6.6.2", "System.Text.Json": "4.6.0"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.6.2": {"dependencies": {"Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.AspNetCore.StaticFiles": "2.1.0", "Microsoft.Extensions.FileProviders.Embedded": "2.1.0", "System.Text.Json": "4.6.0"}, "runtime": {"lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.Buffers/4.5.1": {"runtime": {"lib/net461/System.Buffers.dll": {}}}, "System.ComponentModel/4.3.0": {}, "System.ComponentModel.Annotations/4.5.0": {"runtime": {"lib/net461/System.ComponentModel.Annotations.dll": {}}}, "System.ComponentModel.Composition/9.0.2": {}, "System.Composition/9.0.2": {"dependencies": {"System.Composition.AttributedModel": "9.0.2", "System.Composition.Convention": "9.0.2", "System.Composition.Hosting": "9.0.2", "System.Composition.Runtime": "9.0.2", "System.Composition.TypedParts": "9.0.2"}}, "System.Composition.AttributedModel/9.0.2": {"runtime": {"lib/net462/System.Composition.AttributedModel.dll": {}}}, "System.Composition.Convention/9.0.2": {"dependencies": {"System.Composition.AttributedModel": "9.0.2"}, "runtime": {"lib/net462/System.Composition.Convention.dll": {}}}, "System.Composition.Hosting/9.0.2": {"dependencies": {"System.Composition.Runtime": "9.0.2"}, "runtime": {"lib/net462/System.Composition.Hosting.dll": {}}}, "System.Composition.Runtime/9.0.2": {"runtime": {"lib/net462/System.Composition.Runtime.dll": {}}}, "System.Composition.TypedParts/9.0.2": {"dependencies": {"System.Composition.AttributedModel": "9.0.2", "System.Composition.Hosting": "9.0.2", "System.Composition.Runtime": "9.0.2"}, "runtime": {"lib/net462/System.Composition.TypedParts.dll": {}}}, "System.Diagnostics.DiagnosticSource/9.0.3": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Memory/4.5.5": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net461/System.Memory.dll": {}}}, "System.Numerics.Vectors/4.5.0": {"runtime": {"lib/net46/System.Numerics.Vectors.dll": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {}, "System.Text.Encodings.Web/4.6.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/System.Text.Encodings.Web.dll": {}}}, "System.Text.Json/4.6.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/net461/System.Text.Json.dll": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {}}}, "System.ValueTuple/4.5.0": {"runtime": {"lib/net461/System.ValueTuple.dll": {}}}, "WeathertImporter/1.0.0": {"dependencies": {"Grassfish.UDC.PluginBase": "1.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "System.ComponentModel.Composition": "9.0.2"}, "runtime": {"WeathertImporter.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"WeatherDataImporter/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Grassfish.UDC.PluginBase/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-YHafoSjQPihwn5HDEsgALPSUfQ5xFxpGh55aC2SUE/DmtxoLroGszOa6PriiM9PE+Lkdm3FGgACoLow/9S+U2Q==", "path": "grassfish.udc.pluginbase/1.0.3", "hashPath": "grassfish.udc.pluginbase.1.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-7hfl2DQoATexr0OVw8PwJSNqnu9gsbSkuHkwmHdss5xXCuY2nIfsTjj2NoKeGtp6N94ECioAP78FUfFOMj+TTg==", "path": "microsoft.aspnetcore.authentication.abstractions/2.1.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NKbmBzPW2zTaZLNKkCIL7LMpr4XfXVOPJ5SNzikTe2PX3juLkupb/5oTF45wiw5srUbU6QD0cY9u3jgYUELwnQ==", "path": "microsoft.aspnetcore.authentication.core/2.1.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-QUMtMVY7mQeJWlP8wmmhZf1HEGM/V8prW/XnYeKDpEniNBCRw0a3qktRb9aBU0vR+bpJwWZ0ibcB8QOvZEmDHQ==", "path": "microsoft.aspnetcore.authorization/2.1.0", "hashPath": "microsoft.aspnetcore.authorization.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-e/wxbmwHza+Y6hmM/xiQdsVX5Xh0cPHFbDTGR3kIK7a+jyBSc8CPAJOA5g0ziikLEp5Cm/Qux+CsWad53QoNOw==", "path": "microsoft.aspnetcore.authorization.policy/2.1.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1TQgBfd/NPZLR2o/h6l5Cml2ZCF5hsyV4h9WEwWwAIavrbdTnaNozGGcTOd4AOgQvogMM9UM1ajflm9Cwd0jLQ==", "path": "microsoft.aspnetcore.hosting.abstractions/2.1.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-YTKMi2vHX6P+WHEVpW/DS+eFHnwivCSMklkyamcK1ETtc/4j8H3VR0kgW8XIBqukNxhD8k5wYt22P7PhrWSXjQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.1.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-e<PERSON>ryjDRH41EYY2sOMHCu+tHXLI6PUN1AsOPKst6GbiIoMi8wJCiPcE4h9418tKje1oUzmMc2Iz8fFPPVamfaw==", "path": "microsoft.aspnetcore.http/2.1.0", "hashPath": "microsoft.aspnetcore.http.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vbFDyKsSYBnxl3+RABtN79b0vsTcG66fDY8vD6Nqvu9uLtSej70Q5NcbGlnN6bJpZci5orSdgFTHMhBywivDPg==", "path": "microsoft.aspnetcore.http.abstractions/2.1.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-M8Gk5qrUu5nFV7yE3SZgATt/5B1a5Qs8ZnXXeO/Pqu68CEiBHJWc10sdGdO5guc3zOFdm7H966mVnpZtEX4vSA==", "path": "microsoft.aspnetcore.http.extensions/2.1.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UmkUePxRjsQW0j5euFFscBwjvTu25b8+qIK/2fI3GvcqQ+mkwgbWNAT8b/Gkoei1m2bTWC07lSdutuRDPPLcJA==", "path": "microsoft.aspnetcore.http.features/2.1.0", "hashPath": "microsoft.aspnetcore.http.features.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NhocJc6vRjxjM8opxpbjYhdN7WbsW07eT5hZOzv87bPxwEL98Hw+D+JIu9DsPm0ce7Rao1qN1BP7w8GMhRFH0Q==", "path": "microsoft.aspnetcore.mvc.abstractions/2.1.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.ApiExplorer/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UEmXDfLhy9OaTH4t2iXFnPYf1UBpwCM4tdeBoQyGn/pvKEA/TTvxO4K1dC6kF8x5l/IbtLI+ud0rcPttjdyYUA==", "path": "microsoft.aspnetcore.mvc.apiexplorer/2.1.0", "hashPath": "microsoft.aspnetcore.mvc.apiexplorer.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-AtNtFLtFgZglupwiRK/9ksFg1xAXyZ1otmKtsNSFn9lIwHCQd1xZHIph7GTZiXVWn51jmauIUTUMSWdpaJ+f+A==", "path": "microsoft.aspnetcore.mvc.core/2.1.0", "hashPath": "microsoft.aspnetcore.mvc.core.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.DataAnnotations/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHcJKYjf8ASL++LVMGYZTa3NYVWXKBwDmP1sBIVWD21wDI58/b+NkUiGrcKxmG4VAYrHnEy6XkLbXxh0gVmBRw==", "path": "microsoft.aspnetcore.mvc.dataannotations/2.1.0", "hashPath": "microsoft.aspnetcore.mvc.dataannotations.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ht/KGFWYqcUDDi+VMPkQNzY7wQ0I2SdqXMEPl6AsOW8hmO3ZS4jIPck6HGxIdlk7ftL9YITJub0cxBmnuq+6zQ==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.1.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-eRdsCvtUlLsh0O2Q8JfcpTUhv0m5VCYkgjZTCdniGAq7F31B3gNrBTn9VMqz14m+ZxPUzNqudfDFVTAQlrI/5Q==", "path": "microsoft.aspnetcore.routing/2.1.0", "hashPath": "microsoft.aspnetcore.routing.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXmnHeb3v+HTfn74M46s+4wLaMkplj1Yl2pRf+2mfDDsQ7PN0+h8AFtgip5jpvBvFHQ/Pei7S+cSVsSTHE67fQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.1.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.StaticFiles/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-puGNg55r79arhgictNnlOnvk0ODR0MgLwWt2slzgoCiy9uCb5UaDi8rGyoYDRSlNXrXPSVen25Hk/s/gmH2Pow==", "path": "microsoft.aspnetcore.staticfiles/2.1.0", "hashPath": "microsoft.aspnetcore.staticfiles.2.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-xBy8JGXQ3tVSYzLl/LtN3c9EeB75khFSB2Kw2HWmF+McU0Ltva7R4JBRH0Rb4LgkcjYyyJdf+09PZalQFwsT+Q==", "path": "microsoft.aspnetcore.webutilities/2.1.0", "hashPath": "microsoft.aspnetcore.webutilities.2.1.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-oFFX9Ls8dnNUBCD9yzRzHTY8tqvv+CiX43B8L8DjrM8BqYTAlORYaJf6+KXNtSC2bD1135yV8OxzcZFaluow5w==", "path": "microsoft.bcl.asyncinterfaces/9.0.3", "hashPath": "microsoft.bcl.asyncinterfaces.9.0.3.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "path": "microsoft.dotnet.platformabstractions/2.1.0", "hashPath": "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-lMmUjAKvY9r6QmxCS15iSb6ulhwnh0zp44NtnVJ+HIDLFmu4iej41U+dU58On8NRezmlgRXiQtLnBeZSzYNKQg==", "path": "microsoft.extensions.configuration.abstractions/2.1.0", "hashPath": "microsoft.extensions.configuration.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-lDbxJpkl6X8KZGpkAxgrrthQ42YeiR0xjPp7KPx+sCPc3ZbpaIbjzd0QQ+9kDdK2RU2DOl3pc6tQyAgEZY3V0A==", "path": "microsoft.extensions.dependencyinjection/9.0.3", "hashPath": "microsoft.extensions.dependencyinjection.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-TfaHPSe39NyL2wxkisRxXK7xvHGZYBZ+dy3r+mqGvnxKgAPdHkMu3QMQZI4pquP6W5FIQBqs8FJpWV8ffCgDqQ==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.3", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "path": "microsoft.extensions.dependencymodel/2.1.0", "hashPath": "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-itv+7XBu58pxi8mykxx9cUO1OOVYe0jmQIZVSZVp5lOcLxB7sSV2bnHiI1RSu6Nxne/s6+oBla3ON5CCMSmwhQ==", "path": "microsoft.extensions.fileproviders.abstractions/2.1.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Zh8BkC4aneMITeGzflkDZKDkgDC+YhsvjAaeDLWEmgzNoAGPLbH+tbvn9Boo94V0cryfR/5396goB5KAElKzfw==", "path": "microsoft.extensions.fileproviders.embedded/2.1.0", "hashPath": "microsoft.extensions.fileproviders.embedded.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BpMaoBxdXr5VD0yk7rYN6R8lAU9X9JbvsPveNdKT+llIn3J5s4sxpWqaSG/NnzTzTLU5eJE5nrecTl7clg/7dQ==", "path": "microsoft.extensions.hosting.abstractions/2.1.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-EssCj6ZGhZL6ojL8IKLxJEIEgz8RQQHO1ZUgW6uIUoyzcwCMTEnN9mQWDXcXAitx0tyNz8xKNudTJ1RCc3/lkA==", "path": "microsoft.extensions.localization/2.1.0", "hashPath": "microsoft.extensions.localization.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-i5LgUcc0OB4KMujLmjdpDsha72xLa7CQwpG3zEEa9SRC5k8qrSyx97flmiikq61sVWzPrHXjYaj3jaZC6z+TBw==", "path": "microsoft.extensions.localization.abstractions/2.1.0", "hashPath": "microsoft.extensions.localization.abstractions.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-utIi2R1nm+PCWkvWBf1Ou6LWqg9iLfHU23r8yyU9VCvda4dEs7xbTZSwGa5KuwbpzpgCbHCIuKaFHB3zyFmnGw==", "path": "microsoft.extensions.logging/9.0.3", "hashPath": "microsoft.extensions.logging.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-H/MBMLt9A/69Ux4OrV7oCKt3DcMT04o5SCqDolulzQA66TLFEpYYb4qedMs/uwrLtyHXGuDGWKZse/oa8W9AZw==", "path": "microsoft.extensions.logging.abstractions/9.0.3", "hashPath": "microsoft.extensions.logging.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tIbO45cohqexTJPXBubpwluycDT+6OWy2m7PukG37XMrtQ6Zv4AnoLrgUTaCmpWihSs5RZHKvThiAJFcBlR3AA==", "path": "microsoft.extensions.objectpool/2.1.0", "hashPath": "microsoft.extensions.objectpool.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-xE7MpY70lkw1oiid5y6FbL9dVw8oLfkx8RhSNGN8sSzBlCqGn0SyT3Fqc8tZnDaPIq7Z8R9RTKlS564DS+MV3g==", "path": "microsoft.extensions.options/9.0.3", "hashPath": "microsoft.extensions.options.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-yCCJHvBcRyqapMSNzP+kTc57Eaavq2cr5Tmuil6/XVnipQf5xmskxakSQ1enU6S4+fNg3sJ27WcInV64q24JsA==", "path": "microsoft.extensions.primitives/9.0.3", "hashPath": "microsoft.extensions.primitives.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-YwzwLadahZiEbqbDoAlKhAq/szBL05ZmIIlrfHjLsF9M3zppmWRKAOGjFalmwONxbZMl3OHUoAiPKShtieV0KA==", "path": "microsoft.extensions.webencoders/2.1.0", "hashPath": "microsoft.extensions.webencoders.2.1.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-c08F7C7BGgmjrq9cr7382pBRhcimBx24YOv4M4gtzMIuVKmxGoRr5r9A2Hke9v7Nx7zKKCysk6XpuZasZX4oeg==", "path": "microsoft.net.http.headers/2.1.0", "hashPath": "microsoft.net.http.headers.2.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.14": {"type": "package", "serviceable": true, "sha512": "sha512-tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw==", "path": "microsoft.openapi/1.6.14", "hashPath": "microsoft.openapi.1.6.14.nupkg.sha512"}, "Newtonsoft.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-U82mHQSKaIk+lpSVCbWYKNavmNH1i5xrExDEquU1i6I5pV6UMOqRnJRSlKO3cMPfcpp0RgDY+8jUXHdQ4IfXvw==", "path": "newtonsoft.json/9.0.1", "hashPath": "newtonsoft.json.9.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-+NB4UYVYN6AhDSjW0IJAd1AGD8V33gemFNLPaxKTtPkHB+HaKAKf9MGAEUPivEWvqeQfcKIw8lJaHq6LHljRuw==", "path": "swashbuckle.aspnetcore/6.6.2", "hashPath": "swashbuckle.aspnetcore.6.6.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-ovgPTSYX83UrQUWiS5vzDcJ8TEX1MAxBgDFMK45rC24MorHEPQlZAHlaXj/yth4Zf6xcktpUgTEBvffRQVwDKA==", "path": "swashbuckle.aspnetcore.swagger/6.6.2", "hashPath": "swashbuckle.aspnetcore.swagger.6.6.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-zv4ikn4AT1VYuOsDCpktLq4QDq08e7Utzbir86M5/ZkRaLXbCPF11E1/vTmOiDzRTl0zTZINQU2qLKwTcHgfrA==", "path": "swashbuckle.aspnetcore.swaggergen/6.6.2", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.6.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-mBBb+/8Hm2Q3Wygag+hu2jj69tZW5psuv0vMRXY07Wy+Rrj40vRP8ZTbKBhs91r45/HXT4aY4z0iSBYx1h6JvA==", "path": "swashbuckle.aspnetcore.swaggerui/6.6.2", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.6.2.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg==", "path": "system.componentmodel.annotations/4.5.0", "hashPath": "system.componentmodel.annotations.4.5.0.nupkg.sha512"}, "System.ComponentModel.Composition/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-a0U4avrdO1YZfLY3YmjuRIFN3D9ufNBxtXyRVr7JwM0qay0e5EGORnbQ2od8VV53UWDAO4ZePbjXC169uzmZ8w==", "path": "system.componentmodel.composition/9.0.2", "hashPath": "system.componentmodel.composition.9.0.2.nupkg.sha512"}, "System.Composition/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-4xBv1vlceeYDtAoeC6xo74HTY8vV6TbAWxkHjo5y/4wq13ex4nTgRHjHobR6p2MF65Ev1BtOH5xQLEnCHhtBsA==", "path": "system.composition/9.0.2", "hashPath": "system.composition.9.0.2.nupkg.sha512"}, "System.Composition.AttributedModel/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-XgQstWRsZFpOR19Bkfrnj2uad+WNDMqoLCAHuhxoiuSMNYhI1aYKcTHNFEpJvk87Yi8pKPEjRorb/TrkAM2iCw==", "path": "system.composition.attributedmodel/9.0.2", "hashPath": "system.composition.attributedmodel.9.0.2.nupkg.sha512"}, "System.Composition.Convention/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON><PERSON>tip6Kkc4S74skkZb8eus21iXuwfV7HdoAf396uIxlofmKpQstWqVgS0DcWQes4SfY8rGcy3spZGg9iAriw==", "path": "system.composition.convention/9.0.2", "hashPath": "system.composition.convention.9.0.2.nupkg.sha512"}, "System.Composition.Hosting/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-A97et4A4gLgwvjeYcQQ7lUOigiaBb6BvZ8qV5xxSzQyuCMm8VBBzpcVA0c9/F1pNVuaIt+2kdCOSp+XqpeErjQ==", "path": "system.composition.hosting/9.0.2", "hashPath": "system.composition.hosting.9.0.2.nupkg.sha512"}, "System.Composition.Runtime/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-W+M/KX93FwJ3JMB8bP3bBK+9p/zOrocowg9dd2H1i0dcHlQk4agXk2o4qYJHQDYyUpUrf3BsMiFRKvPMZHZasA==", "path": "system.composition.runtime/9.0.2", "hashPath": "system.composition.runtime.9.0.2.nupkg.sha512"}, "System.Composition.TypedParts/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-YUH7bPkodtbSkCS1jSVBdInILKNO3kHB4nzYknAelRqPbzoWEBnrQHwp3OHOMJx46cgQIO5t9KYxLuYVopmt1Q==", "path": "system.composition.typedparts/9.0.2", "hashPath": "system.composition.typedparts.9.0.2.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-cBA+28xDW33tSiGht/H8xvr8lnaCrgJ7EdO348AfSGsX4PPJUOULKxny/cc9DVNGExaCrtqagsnm5M2mkWIZ+g==", "path": "system.diagnostics.diagnosticsource/9.0.3", "hashPath": "system.diagnostics.diagnosticsource.9.0.3.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-BXgFO8Yi7ao7hVA/nklD0Hre1Bbce048ZqryGZVFifGNPuh+2jqF1i/jLJLMfFGZIzUOw+nCIeH24SQhghDSPw==", "path": "system.text.encodings.web/4.6.0", "hashPath": "system.text.encodings.web.4.6.0.nupkg.sha512"}, "System.Text.Json/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-4F8Xe+JIkVoDJ8hDAZ7HqLkjctN/6WItJIzQaifBwClC7wmoLSda/Sv2i6i1kycqDb3hWF4JCVbpAweyOKHEUA==", "path": "system.text.json/4.6.0", "hashPath": "system.text.json.4.6.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "WeathertImporter/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Composition.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.WindowsRuntime/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Duplex/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Http/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.NetTcp/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Primitives/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Security/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netfx.force.conflicts/0.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}}