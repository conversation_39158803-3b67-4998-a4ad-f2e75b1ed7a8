{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\wgm-dotnet-udc-weatherdataimporter\\WeatherDataImporterTest\\WeatherDataImporterTest.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\wgm-dotnet-udc-weatherdataimporter\\WeatherDataImporterTest\\WeatherDataImporterTest.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\wgm-dotnet-udc-weatherdataimporter\\WeatherDataImporterTest\\WeatherDataImporterTest.csproj", "projectName": "WeatherDataImporterTest", "projectPath": "C:\\Users\\<USER>\\source\\repos\\wgm-dotnet-udc-weatherdataimporter\\WeatherDataImporterTest\\WeatherDataImporterTest.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\wgm-dotnet-udc-weatherdataimporter\\WeatherDataImporterTest\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://grassfish.jfrog.io/artifactory/api/nuget/v3/default-nuget-virtual/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.5.0, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.2.0, )"}, "xunit": {"target": "Package", "version": "[2.4.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}}}