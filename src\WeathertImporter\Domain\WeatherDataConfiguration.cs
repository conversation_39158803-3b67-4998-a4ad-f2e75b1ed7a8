﻿namespace WigevDotnetUdcWeatherDataImporter.Domain
{
    using System;
    using Grassfish.UDC.PluginBase.V2.Interfaces;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using WeathertImporter.Domain.Models;

    /// <summary>
    /// Holds all information necessary to query the Weather FTP server.
    /// </summary>
    public class WeatherDataConfiguration
    {
        public WeatherDataConfiguration(string ftpServerUrl, string username, string password)
        {
            this.FtpServerUrl = new Uri(ftpServerUrl);
            this.Username = username;
            this.Password = password; 
        }
        public Uri FtpServerUrl { get; }
        public string Username { get; }
        public string Password { get; }
    }
}