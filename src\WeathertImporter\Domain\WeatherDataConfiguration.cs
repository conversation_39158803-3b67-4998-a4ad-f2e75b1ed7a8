﻿namespace WigevDotnetUdcWeatherDataImporter.Domain
{
    using System;
    using Grassfish.UDC.PluginBase.V2.Interfaces;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using WeathertImporter.Domain.Models;

    /// <summary>
    /// Holds all information necessary to query the met.no Weather API.
    /// </summary>
    public class WeatherDataConfiguration
    {
        public WeatherDataConfiguration(string cityName, string latitude, string longitude)
        {
            this.CityName = cityName;
            this.Latitude = latitude;
            this.Longitude = longitude;
        }
        public string CityName { get; }
        public string Latitude { get; }
        public string Longitude { get; }
    }
}