﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WigevDotnetUdcWeatherDataImporter.Domain.Models;
using Grassfish.UDC.PluginBase.V2;
using Grassfish.UDC.PluginBase.V2.Interfaces;
using Microsoft.Extensions.Logging;
using WigevDotnetUdcWeatherDataImporter.Logging;
using WigevDotnetUdcWeatherDataImporter.Domain;
using WigevDotnetUdcWeatherDataImporter.Application;

namespace WigevDotnetUdcWeatherDataImporter.Domain.Importer
{
    public class WeatherDataImporter : IWeatherDataImporter
    {
        private readonly IWeatherDataService _weatherDataService;
        private readonly IPluginLogger _logger;

        public WeatherDataImporter(IWeatherDataService weatherDataService, IPluginLogger logger)
        {
            _weatherDataService = weatherDataService;
            _logger = logger;
        }

        public async Task<ImportResult<UdcElement>> ImportAsync(IFeed feed)
        {
            _logger.LogDebug($"Starting weather data import from feed: {feed.Source} and {feed.Username}");
            var result = new ImportResult<UdcElement>();
            try
            {
                var config = new WeatherDataConfiguration(feed.Name, feed.Username, feed.Password);
                List<WeatherForecast> forecasts = (await _weatherDataService.FetchWeatherData(config)).ToList();
                List<UdcElement> udcElements = forecasts.Select(UdcElement.Create).ToList();
                foreach (var udcElement in udcElements)
                {
                    var category = result.FindCategoryOrDefault(udcElement.City);
                    if (category is null)
                    {
                        category = new Category(udcElement.City, udcElement.City);
                        result.StoreCategory(category);
                    }

                    udcElement.AddCategory(category);
                    result.StoreElement(udcElement);
                }

                result.Success = true;
                _logger.LogDebug($"Successfully imported {udcElements.Count} weather elements.");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during weather data import: {ex.Message}");
                result.Success = false;
            }
            return result;
        }
    }
}