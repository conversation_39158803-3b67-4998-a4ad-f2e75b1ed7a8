﻿namespace WigevDotnetUdcWeatherDataImporter.Logging
{
    using System;
    using Grassfish.UDC.PluginBase.V2;

    /// <summary>
    /// Uses the delegates registered in <see cref="PluginLoggingDelegates"/> to log using the logging infrastructure
    /// provided by <see cref="PluginBase{TElement}"/>. This is done so we can easily inject logging into our services.
    /// </summary>
    public class PluginLogger : IPluginLogger
    {
        private readonly PluginLoggingDelegates _pluginLoggingDelegates;

        public PluginLogger(PluginLoggingDelegates pluginLoggingDelegates)
        {
            this._pluginLoggingDelegates = pluginLoggingDelegates;
        }

        public void LogDebug(string message, params object[] logValues) => Log(LogLevel.Debug, message, null, logValues);
        public void LogInfo(string message, params object[] logValues) => Log(LogLevel.Information, message, null, logValues);
        public void LogWarning(string message, params object[] logValues) => Log(LogLevel.Warning, message, null, logValues);
        public void LogError(string message, Exception exception, params object[] logValues) => Log(LogLevel.Error, message, exception, logValues);
        public void LogError(string message, Exception exception) => Log(LogLevel.Error, message, exception);
        public void LogError(Exception exception) => Log(LogLevel.Error, "", exception);

        public void Log(LogLevel logLevel, string message, Exception exception, params object[] logValues)
        {
            if (exception != null)
            {
                message = $"{message} Exception: {exception}";
            }

            switch (logLevel)
            {
                case LogLevel.Debug:
                    this._pluginLoggingDelegates.LogDebugAction(message, logValues);
                    break;
                case LogLevel.Information:
                    this._pluginLoggingDelegates.LogInfoAction(message, logValues);
                    break;
                case LogLevel.Warning:
                    this._pluginLoggingDelegates.LogWarningAction(message, logValues);
                    break;
                case LogLevel.Error:
                    this._pluginLoggingDelegates.LogErrorAction(message, logValues);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(logLevel), logLevel, null);
            }
        }

        void IPluginLogger.LogError(string v)
        {
            Log(LogLevel.Error, v, null);
        }
    }
}
