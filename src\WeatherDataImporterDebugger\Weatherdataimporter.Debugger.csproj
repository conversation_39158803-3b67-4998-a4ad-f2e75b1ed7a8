﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net462</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>disable</ImplicitUsings>
    <RootNamespace>WigevDotnetUdcWeatherDataImporter</RootNamespace>
    <OutputType>Exe</OutputType>
    <AssemblyName>WeatherDataImporter</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Grassfish.UDC.PluginBase" Version="1.0.3" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="System.ComponentModel" Version="4.3.0" />
    <PackageReference Include="System.ComponentModel.Composition" Version="9.0.2" />
    <PackageReference Include="System.Composition" Version="9.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WeathertImporter\WeathertImporter.csproj" />
  </ItemGroup>

</Project>
