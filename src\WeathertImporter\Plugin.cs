﻿namespace WigevDotnetUdcWeatherDataImporter
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.Composition;
    using System.Threading;
    using WigevDotnetUdcWeatherDataImporter.Logging;
    using Grassfish.UDC.PluginBase.Enums;
    using Grassfish.UDC.PluginBase.V2;
    using Grassfish.UDC.PluginBase.V2.Interfaces;
    using Microsoft.Extensions.DependencyInjection;
    using WigevDotnetUdcWeatherDataImporter.Domain.Models;
    using WigevDotnetUdcWeatherDataImporter.Domain.Importer;
    using System.Linq;

    [Export(typeof(PluginBase<>))]
    public sealed class Plugin : PluginBase<UdcElement>
    {
        public override Guid Guid => PluginMetadata.PluginGuid;
        public override byte VersionMajor => PluginMetadata.VersionMajor;
        public override byte VersionMinor => PluginMetadata.VersionMinor;
        public override string Name => PluginMetadata.Name;
        public override string NameShort => PluginMetadata.NameShort;
        public override DisplayType DisplayType => DisplayType.Specific;
        public override bool RequiresLocationData => false; 

        public override IEnumerable<FeedDefinition> FeedDefinitions => new List<FeedDefinition>
        {
            new FeedDefinition(PluginMetadata.FeedGuid,
                "Import from FTP Server",
                FeedDefinitionType.Ftp,
                FeedUpdateType.Full
            ),
        };

        public override bool DisableCategoryFilter => false;
        public override bool DisableElementFilter => false;

        private readonly ServiceProvider _serviceProvider;

        [ImportingConstructor]
        public Plugin()
        {
            var pluginLoggingDelegates = new PluginLoggingDelegates(
                this.LogError,
                this.LogWarn,
                this.LogInfo,
                this.LogDebug
            );

            var serviceCollection = DependencyInjection.CreateServiceCollection(pluginLoggingDelegates);
            this._serviceProvider = serviceCollection.BuildServiceProvider();
        }

        public Plugin(ServiceProvider serviceProvider)
        {
            this._serviceProvider = serviceProvider;
        }

        public override ImportResult<UdcElement> Import(CancellationToken cancellationToken,
            IImportParameters<ICustomer, IFeed, ILocationData> importParameters)
        {
            var logger = this._serviceProvider.GetRequiredService<IPluginLogger>();

            try
            {
                var importer = this._serviceProvider.GetRequiredService<IWeatherDataImporter>();
                var result = importer.ImportAsync(importParameters.FeedToImport)
                    .GetAwaiter()
                    .GetResult();

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex);
                return new ImportResult<UdcElement> { Success = false, ErrorMessage = ex.Message };
            }
        }
    }
}

