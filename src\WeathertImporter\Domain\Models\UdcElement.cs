﻿namespace WigevDotnetUdcWeatherDataImporter.Domain.Models
{
    using Grassfish.UDC.PluginBase.V2;
    using Grassfish.UDC.PluginBase.V2.Attributes;
    public class UdcElement: ElementBase
    {
        private UdcElement(string externalId, string name) : base(externalId, name)
        {
        }

        [UdcProperty("c087d64b-0517-47ce-b388-9210f0c01e46")]
        public string City { get; set; }


        [UdcProperty("d66144fd-d8bc-425a-8085-3c03d650e434")]
        public string Date { get; set; }

        [UdcProperty("09f5f6bc-d5cd-46ed-8e72-e8c1e3a38063")]
        public int TempMax { get; set; }

        [UdcProperty("e64697cb-fbbc-4ffa-a55f-35cd99d63f80")]
        public int TempMin { get; set; }

        [UdcProperty("46ee3e01-a79b-4c6f-99c1-fa9efa0b2fb3")]
        public int WeatherId { get; set; }

        [UdcProperty("618be7be-688a-4238-9150-67da188e2eb1")]
        public int WindSpeed { get; set; }

        //Factory Method
        public static UdcElement Create(WeatherForecast weatherForecast)
        {
            return new UdcElement($"City:{weatherForecast.City},Date:{weatherForecast.Date}",$"Weather Data for:{weatherForecast.City}")
            {
                City = weatherForecast.City,
                Date = weatherForecast.Date,
                TempMax = weatherForecast.TempMax,
                TempMin = weatherForecast.TempMin,
                WeatherId = weatherForecast.WeatherId,
                WindSpeed = weatherForecast.WindSpeed,
            
            };
        }
    }

}
