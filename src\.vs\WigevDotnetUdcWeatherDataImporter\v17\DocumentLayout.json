{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{963B7735-5C98-4421-935F-CF5AA4642799}|WeatherDataImporterDebugger\\Weatherdataimporter.Debugger.csproj|c:\\users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\weatherdataimporterdebugger\\weatherdata.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{963B7735-5C98-4421-935F-CF5AA4642799}|WeatherDataImporterDebugger\\Weatherdataimporter.Debugger.csproj|solutionrelative:weatherdataimporterdebugger\\weatherdata.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{963B7735-5C98-4421-935F-CF5AA4642799}|WeatherDataImporterDebugger\\Weatherdataimporter.Debugger.csproj|c:\\users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\weatherdataimporterdebugger\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{963B7735-5C98-4421-935F-CF5AA4642799}|WeatherDataImporterDebugger\\Weatherdataimporter.Debugger.csproj|solutionrelative:weatherdataimporterdebugger\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|c:\\users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\weathertimporter\\domain\\models\\weatherforecast.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|solutionrelative:weathertimporter\\domain\\models\\weatherforecast.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|c:\\users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\weathertimporter\\domain\\weatherdataconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|solutionrelative:weathertimporter\\domain\\weatherdataconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|c:\\users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\weathertimporter\\infrastructure\\weatherdataservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|solutionrelative:weathertimporter\\infrastructure\\weatherdataservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|c:\\users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\weathertimporter\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|solutionrelative:weathertimporter\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|c:\\users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\weathertimporter\\plugin.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|solutionrelative:weathertimporter\\plugin.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|c:\\users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\weathertimporter\\domain\\importer\\weatherdataimporter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|solutionrelative:weathertimporter\\domain\\importer\\weatherdataimporter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|c:\\users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\weathertimporter\\domain\\importer\\iweatherdataimporter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{16485CA1-9264-41AC-B735-2049D878376A}|WeathertImporter\\WeathertImporter.csproj|solutionrelative:weathertimporter\\domain\\importer\\iweatherdataimporter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "weatherData.xml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeatherDataImporterDebugger\\weatherData.xml", "RelativeDocumentMoniker": "WeatherDataImporterDebugger\\weatherData.xml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeatherDataImporterDebugger\\weatherData.xml", "RelativeToolTip": "WeatherDataImporterDebugger\\weatherData.xml", "ViewState": "AgIAABMAAAAAAAAAAAAAABYAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-04-05T04:51:51.44Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "WeatherDataService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Infrastructure\\WeatherDataService.cs", "RelativeDocumentMoniker": "WeathertImporter\\Infrastructure\\WeatherDataService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Infrastructure\\WeatherDataService.cs", "RelativeToolTip": "WeathertImporter\\Infrastructure\\WeatherDataService.cs", "ViewState": "AgIAAIMAAAAAAAAAAAAAwFQAAABaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-05T04:49:03.946Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "WeatherForecast.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Domain\\Models\\WeatherForecast.cs", "RelativeDocumentMoniker": "WeathertImporter\\Domain\\Models\\WeatherForecast.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Domain\\Models\\WeatherForecast.cs", "RelativeToolTip": "WeathertImporter\\Domain\\Models\\WeatherForecast.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-05T04:48:56.613Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "Plugin.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Plugin.cs", "RelativeDocumentMoniker": "WeathertImporter\\Plugin.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Plugin.cs", "RelativeToolTip": "WeathertImporter\\Plugin.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwEMAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-05T04:38:43.518Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeatherDataImporterDebugger\\Program.cs", "RelativeDocumentMoniker": "WeatherDataImporterDebugger\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeatherDataImporterDebugger\\Program.cs", "RelativeToolTip": "WeatherDataImporterDebugger\\Program.cs", "ViewState": "AgIAACQAAAAAAAAAAAAcwE8AAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-05T04:38:58.937Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "WeatherDataConfiguration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Domain\\WeatherDataConfiguration.cs", "RelativeDocumentMoniker": "WeathertImporter\\Domain\\WeatherDataConfiguration.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Domain\\WeatherDataConfiguration.cs", "RelativeToolTip": "WeathertImporter\\Domain\\WeatherDataConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T13:06:01.176Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "WeatherDataImporter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Domain\\Importer\\WeatherDataImporter.cs", "RelativeDocumentMoniker": "WeathertImporter\\Domain\\Importer\\WeatherDataImporter.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Domain\\Importer\\WeatherDataImporter.cs", "RelativeToolTip": "WeathertImporter\\Domain\\Importer\\WeatherDataImporter.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAqwBEAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-05T04:38:12.456Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "IWeatherDataImporter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Domain\\Importer\\IWeatherDataImporter.cs", "RelativeDocumentMoniker": "WeathertImporter\\Domain\\Importer\\IWeatherDataImporter.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\Domain\\Importer\\IWeatherDataImporter.cs", "RelativeToolTip": "WeathertImporter\\Domain\\Importer\\IWeatherDataImporter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-05T04:37:01.442Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "DependencyInjection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\DependencyInjection.cs", "RelativeDocumentMoniker": "WeathertImporter\\DependencyInjection.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\wigev-dotnet-udc-weatherdataimporter\\src\\WeathertImporter\\DependencyInjection.cs", "RelativeToolTip": "WeathertImporter\\DependencyInjection.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-05T04:24:08.055Z"}]}]}]}