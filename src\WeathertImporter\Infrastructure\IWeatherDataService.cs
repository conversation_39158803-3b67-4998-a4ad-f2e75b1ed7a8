﻿using System;
using WigevDotnetUdcWeatherDataImporter.Domain.Models;
using WigevDotnetUdcWeatherDataImporter.Domain;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace WigevDotnetUdcWeatherDataImporter.Application
{
    public interface IWeatherDataService
    {
        Task<IReadOnlyCollection<WeatherForecast>> FetchWeatherData(WeatherDataConfiguration configuration);
    }
}
