﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net462</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
	  <Nullable>disable</Nullable>
    <AutoGenerateBindingRedirects>True</AutoGenerateBindingRedirects>

    <!-- Publishing configuration to generate minimal output -->
    <CopyLocalLockFileAssemblies>false</CopyLocalLockFileAssemblies>
    <GenerateRuntimeConfigurationFiles>false</GenerateRuntimeConfigurationFiles>
    <GenerateDependencyFile>false</GenerateDependencyFile>
    <DebugType>portable</DebugType>
  </PropertyGroup>
	
  <ItemGroup>
    <PackageReference Include="Grassfish.UDC.PluginBase" Version="1.0.3">
	  <IncludeAssets>compile</IncludeAssets>
	</PackageReference>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.ComponentModel.Composition" Version="9.0.2" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <Reference Update="System.Runtime.Serialization">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Core">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Data">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Drawing">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.IO.Compression.FileSystem">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Numerics">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Xml.Linq">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Update="System.Xml">
      <Private>True</Private>
    </Reference>
  </ItemGroup>

  <!-- Custom publish target to copy only main DLL and PDB -->
  <Target Name="PublishMinimal" AfterTargets="Build">
    <PropertyGroup>
      <PublishMinimalDir>$(OutputPath)publish-minimal\</PublishMinimalDir>
    </PropertyGroup>

    <ItemGroup>
      <MainFiles Include="$(OutputPath)WeathertImporter.dll" />
      <MainFiles Include="$(OutputPath)WeathertImporter.pdb" />
    </ItemGroup>

    <MakeDir Directories="$(PublishMinimalDir)" />
    <Copy SourceFiles="@(MainFiles)" DestinationFolder="$(PublishMinimalDir)" />

    <Message Text="Minimal publish completed. Files copied to: $(PublishMinimalDir)" Importance="high" />
  </Target>

  <!-- Standalone target for manual minimal publishing -->
  <Target Name="PublishMinimalOnly" DependsOnTargets="Build">
    <PropertyGroup>
      <PublishMinimalDir>$(OutputPath)publish-minimal\</PublishMinimalDir>
    </PropertyGroup>

    <ItemGroup>
      <MainFiles Include="$(OutputPath)WeathertImporter.dll" />
      <MainFiles Include="$(OutputPath)WeathertImporter.pdb" />
    </ItemGroup>

    <RemoveDir Directories="$(PublishMinimalDir)" />
    <MakeDir Directories="$(PublishMinimalDir)" />
    <Copy SourceFiles="@(MainFiles)" DestinationFolder="$(PublishMinimalDir)" />

    <Message Text="Minimal publish completed. Only WeathertImporter.dll and WeathertImporter.pdb copied to: $(PublishMinimalDir)" Importance="high" />
  </Target>

</Project>
