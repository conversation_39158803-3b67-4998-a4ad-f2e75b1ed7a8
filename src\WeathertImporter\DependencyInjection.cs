﻿namespace WigevDotnetUdcWeatherDataImporter
{
    using WigevDotnetUdcWeatherDataImporter.Logging;
    using Microsoft.Extensions.DependencyInjection;
    using WigevDotnetUdcWeatherDataImporter.Domain.Importer;
    using WigevDotnetUdcWeatherDataImporter.Application;

    public class DependencyInjection
    {
        /// <summary>
        /// Registers all necessary classes for the Plugin
        /// </summary>
        /// <returns></returns>
        public static ServiceCollection CreateServiceCollection(PluginLoggingDelegates pluginLoggingDelegates)
        {
            var serviceCollection = new ServiceCollection();
            serviceCollection

                .AddSingleton<IPluginLogger>(new PluginLogger(pluginLoggingDelegates))
                // Register the other dependencies
                .AddSingleton<IWeatherDataService, WeatherDataService>()
                .AddSingleton<IWeatherDataImporter, WeatherDataImporter>();
            return serviceCollection;
        }
    }
}
