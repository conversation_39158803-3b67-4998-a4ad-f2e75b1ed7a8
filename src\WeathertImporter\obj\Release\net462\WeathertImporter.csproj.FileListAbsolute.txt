C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Buffers.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Memory.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.ValueTuple.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.AssemblyInfo.cs
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.sourcelink.json
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\Weathert.33F4B0CF.Up2Date
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wgm-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\FluentFTP.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Buffers.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Memory.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\bin\Release\net462\System.ValueTuple.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.AssemblyInfo.cs
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.sourcelink.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\Weathert.33F4B0CF.Up2Date
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\WeathertImporter\obj\Release\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Buffers.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Memory.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Numerics.Vectors.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.ValueTuple.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\obj\Release\net462\WeathertImporter.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\obj\Release\net462\WeathertImporter.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\obj\Release\net462\WeathertImporter.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\obj\Release\net462\WeathertImporter.AssemblyInfo.cs
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\obj\Release\net462\WeathertImporter.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\obj\Release\net462\WeathertImporter.sourcelink.json
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\obj\Release\net462\Weathert.33F4B0CF.Up2Date
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\obj\Release\net462\WeathertImporter.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\obj\Release\net462\WeathertImporter.pdb
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Net.Http.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\source\repos\wigev-dotnet-udc-weatherdataimporter\src\WeathertImporter\bin\Release\net462\System.Security.Cryptography.X509Certificates.dll
