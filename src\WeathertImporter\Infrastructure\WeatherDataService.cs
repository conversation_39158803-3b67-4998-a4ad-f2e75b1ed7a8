﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Runtime.CompilerServices;
using System.Xml;
using WigevDotnetUdcWeatherDataImporter.Domain;
using WigevDotnetUdcWeatherDataImporter.Domain.Models;
using System.Linq;
using System.Threading.Tasks;
using WigevDotnetUdcWeatherDataImporter.Logging;
using FluentFTP;
using System.Text;

namespace WigevDotnetUdcWeatherDataImporter.Application
{

    public class WeatherDataService : IWeatherDataService
    {
        private readonly IPluginLogger _logger;

        public WeatherDataService(IPluginLogger logger)
        {
            _logger = logger;
        }

        public async Task<IReadOnlyCollection<WeatherForecast>> FetchWeatherData(WeatherDataConfiguration weatherDataConfiguration)
        {
            try
            {
                string xmlData = FetchXmlFromFtp(weatherDataConfiguration.FtpServerUrl.ToString(), weatherDataConfiguration.Username.ToString(), weatherDataConfiguration.Password.ToString());

                _logger.LogDebug("Downloaded XML: " + xmlData);

                if (string.IsNullOrWhiteSpace(weatherDataConfiguration.FtpServerUrl?.ToString()))
                    _logger.LogDebug("FTP URL is missing or empty.");

                if (string.IsNullOrWhiteSpace(weatherDataConfiguration.Username))
                    _logger.LogDebug("Username is missing or empty.");

                if (string.IsNullOrWhiteSpace(weatherDataConfiguration.Password))
                    _logger.LogDebug("Password is missing or empty.");
                
                return await ParseWeatherData(xmlData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                return Array.Empty<WeatherForecast>();
            }
        }
       
        private string FetchXmlFromFtp(string ftpUrl, string username, string password)
        {
            try
            {
                Uri uri = new Uri(ftpUrl);
                string host = uri.Host;
                string filePath = uri.AbsolutePath;

                using (var ftpClient = new FtpClient(host))
                {
                    ftpClient.Credentials = new System.Net.NetworkCredential(username, password);
                    ftpClient.Connect();

                    byte[] fileBytes;
                    bool success = ftpClient.DownloadBytes(out fileBytes, filePath);

                    if (!success || fileBytes == null || fileBytes.Length == 0)
                    {
                        _logger.LogError($"Failed to download file: {filePath} from {ftpUrl}");
                        return string.Empty;
                    }

                    return System.Text.Encoding.UTF8.GetString(fileBytes);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                return string.Empty;
            }
        }

        private Task<IReadOnlyCollection<WeatherForecast>> ParseWeatherData(string xmlData)
        {
            var forecasts = new List<WeatherForecast>();

            try
            {
                if (string.IsNullOrWhiteSpace(xmlData))
                {
                    _logger.LogError("Fetched XML data is empty or null. Skipping parsing.");
                    return Task.FromResult<IReadOnlyCollection<WeatherForecast>>(forecasts.AsReadOnly());
                }

                // Remove BOM if present
                xmlData = RemoveUtf8Bom(xmlData);

                var doc = new XmlDocument();
                doc.LoadXml(xmlData);

                forecasts.AddRange(
                    from XmlNode stadtNode in doc.SelectNodes("//stadt")
                    let cityName = stadtNode["name"]?.InnerText ?? "UnknownCity"
                    from XmlNode prognoseNode in stadtNode.SelectNodes("prognose")
                    let date = prognoseNode.Attributes["datum"]?.InnerText ?? "00.00.0000"
                    let tempMax = prognoseNode["temp_max"] != null && int.TryParse(prognoseNode["temp_max"].InnerText, out var maxTemp) ? maxTemp : 0
                    let tempMin = prognoseNode["temp_min"] != null && int.TryParse(prognoseNode["temp_min"].InnerText, out var minTemp) ? minTemp : 0
                    let weatherId = prognoseNode["id_wetter"] != null && int.TryParse(prognoseNode["id_wetter"].InnerText, out var id) ? id : -1
                    let windSpeed = prognoseNode["windgeschw"] != null && int.TryParse(prognoseNode["windgeschw"].InnerText, out var wind) ? wind : 0
                    select new WeatherForecast
                    {
                        City = cityName,
                        Date = date,
                        TempMax = tempMax,
                        TempMin = tempMin,
                        WeatherId = weatherId,
                        WindSpeed = windSpeed
                    });

                return Task.FromResult<IReadOnlyCollection<WeatherForecast>>(forecasts.AsReadOnly());

                //foreach (XmlNode stadtNode in doc.SelectNodes("//stadt"))
                //{
                //    var cityName = stadtNode["name"]?.InnerText?.Trim();
                //    if (string.IsNullOrEmpty(cityName))
                //        continue;

                //    if (playerCity != null && !playerCity.Contains(cityName.ToLower()))
                //        continue; // skip cities not matching player location

                //    foreach (XmlNode prognoseNode in stadtNode.SelectNodes("prognose"))
                //    {
                //        var date = prognoseNode.Attributes["datum"]?.InnerText ?? "00.00.0000";
                //        var tempMax = prognoseNode["temp_max"] != null && int.TryParse(prognoseNode["temp_max"].InnerText, out var maxTemp) ? maxTemp : 0;
                //        var tempMin = prognoseNode["temp_min"] != null && int.TryParse(prognoseNode["temp_min"].InnerText, out var minTemp) ? minTemp : 0;
                //        var weatherId = prognoseNode["id_wetter"] != null && int.TryParse(prognoseNode["id_wetter"].InnerText, out var id) ? id : -1;
                //        var windSpeed = prognoseNode["windgeschw"] != null && int.TryParse(prognoseNode["windgeschw"].InnerText, out var wind) ? wind : 0;

                //        forecasts.Add(new WeatherForecast
                //        {
                //            City = cityName,
                //            Date = date,
                //            TempMax = tempMax,
                //            TempMin = tempMin,
                //            WeatherId = weatherId,
                //            WindSpeed = windSpeed
                //        });
                //    }
                //}

                //return Task.FromResult<IReadOnlyCollection<WeatherForecast>>(forecasts.AsReadOnly());

            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                return Task.FromResult<IReadOnlyCollection<WeatherForecast>>(forecasts.AsReadOnly());
            }
        }

        private string RemoveUtf8Bom(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var bomChar = Encoding.UTF8.GetString(Encoding.UTF8.GetPreamble());
            if (input.StartsWith(bomChar))
                return input.Remove(0, bomChar.Length);

            return input;
        }
    }
}


