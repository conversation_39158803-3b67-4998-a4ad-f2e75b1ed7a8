﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Xml;
using WigevDotnetUdcWeatherDataImporter.Domain;
using WigevDotnetUdcWeatherDataImporter.Domain.Models;
using System.Linq;
using System.Threading.Tasks;
using WigevDotnetUdcWeatherDataImporter.Logging;
using System.Text;
using Newtonsoft.Json;

namespace WigevDotnetUdcWeatherDataImporter.Application
{

    public class WeatherDataService : IWeatherDataService
    {
        private readonly IPluginLogger _logger;
        private readonly HttpClient _httpClient;
        private const string MetNoApiBaseUrl = "https://api.met.no/weatherapi/locationforecast/2.0/compact";

        public WeatherDataService(IPluginLogger logger)
        {
            _logger = logger;
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "WeatherDataImporter/1.0 (<EMAIL>)");
        }

        public async Task<IReadOnlyCollection<WeatherForecast>> FetchWeatherData(WeatherDataConfiguration weatherDataConfiguration)
        {
            try
            {
                string jsonData = await FetchJsonFromMetNoApi(weatherDataConfiguration.Latitude, weatherDataConfiguration.Longitude);

                _logger.LogDebug("Downloaded JSON: " + jsonData);

                if (string.IsNullOrWhiteSpace(weatherDataConfiguration.Latitude))
                    _logger.LogDebug("Latitude is missing or empty.");

                if (string.IsNullOrWhiteSpace(weatherDataConfiguration.Longitude))
                    _logger.LogDebug("Longitude is missing or empty.");

                if (string.IsNullOrWhiteSpace(weatherDataConfiguration.CityName))
                    _logger.LogDebug("City name is missing or empty.");

                return await ParseWeatherDataFromJson(jsonData, weatherDataConfiguration.CityName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                return Array.Empty<WeatherForecast>();
            }
        }

        private async Task<string> FetchJsonFromMetNoApi(string latitude, string longitude)
        {
            try
            {
                string url = $"{MetNoApiBaseUrl}?lat={latitude}&lon={longitude}";
                _logger.LogDebug($"Fetching weather data from: {url}");

                HttpResponseMessage response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                string jsonContent = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    _logger.LogError($"Empty response from met.no API for coordinates: {latitude}, {longitude}");
                    return string.Empty;
                }

                return jsonContent;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error fetching data from met.no API: {ex.Message}");
                _logger.LogError(ex);
                return string.Empty;
            }
        }

        private Task<IReadOnlyCollection<WeatherForecast>> ParseWeatherDataFromJson(string jsonData, string cityName)
        {
            var forecasts = new List<WeatherForecast>();

            try
            {
                if (string.IsNullOrWhiteSpace(jsonData))
                {
                    _logger.LogError("Fetched JSON data is empty or null. Skipping parsing.");
                    return Task.FromResult<IReadOnlyCollection<WeatherForecast>>(forecasts.AsReadOnly());
                }

                var apiResponse = JsonConvert.DeserializeObject<MetNoApiResponse>(jsonData);

                if (apiResponse?.Properties?.TimeSeries == null)
                {
                    _logger.LogError("Invalid API response structure from met.no");
                    return Task.FromResult<IReadOnlyCollection<WeatherForecast>>(forecasts.AsReadOnly());
                }

                // Group by date and calculate daily min/max temperatures
                var dailyForecasts = apiResponse.Properties.TimeSeries
                    .Where(ts => ts.Data?.Instant?.Details?.AirTemperature.HasValue == true)
                    .GroupBy(ts => ts.Time.Date)
                    .Take(7) // Take first 7 days
                    .Select(group => new WeatherForecast
                    {
                        City = cityName ?? "Unknown",
                        Date = group.Key.ToString("dd.MM.yyyy"),
                        TempMax = (int)Math.Round(group.Max(ts => ts.Data.Instant.Details.AirTemperature.Value)),
                        TempMin = (int)Math.Round(group.Min(ts => ts.Data.Instant.Details.AirTemperature.Value)),
                        WeatherId = ConvertSymbolCodeToWeatherId(group.FirstOrDefault()?.Data?.Next12Hours?.Summary?.SymbolCode ?? "unknown"),
                        WindSpeed = (int)Math.Round(group.Average(ts => ts.Data.Instant.Details.WindSpeed ?? 0))
                    });

                forecasts.AddRange(dailyForecasts);
                return Task.FromResult<IReadOnlyCollection<WeatherForecast>>(forecasts.AsReadOnly());

            }
            catch (Exception ex)
            {
                _logger.LogError($"Error parsing JSON weather data: {ex.Message}");
                _logger.LogError(ex);
                return Task.FromResult<IReadOnlyCollection<WeatherForecast>>(forecasts.AsReadOnly());
            }
        }

        private int ConvertSymbolCodeToWeatherId(string symbolCode)
        {
            // Map met.no symbol codes to weather IDs (simplified mapping)
            if (string.IsNullOrEmpty(symbolCode))
                return 0;

            var code = symbolCode.ToLower();

            if (code == "clearsky_day" || code == "clearsky_night")
                return 1;
            if (code == "fair_day" || code == "fair_night")
                return 2;
            if (code == "partlycloudy_day" || code == "partlycloudy_night")
                return 3;
            if (code == "cloudy")
                return 5;
            if (code == "rainshowers_day" || code == "rainshowers_night")
                return 6;
            if (code == "rain")
                return 7;
            if (code == "sleetshowers_day" || code == "sleetshowers_night")
                return 8;
            if (code == "snow")
                return 9;
            if (code == "snowshowers_day" || code == "snowshowers_night")
                return 10;
            if (code == "fog")
                return 12;
            if (code == "lightrain")
                return 11;
            if (code == "heavyrain")
                return 9;
            if (code == "lightrainshowers_day" || code == "lightrainshowers_night")
                return 9;
            if (code == "heavyrainshowers_day" || code == "heavyrainshowers_night")
                return 9;

            return 0; // Unknown/default
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}


