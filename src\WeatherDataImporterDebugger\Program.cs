using System;
using System.Collections.Generic;
using System.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using WigevDotnetUdcWeatherDataImporter;
using WigevDotnetUdcWeatherDataImporter.Logging;
using Weatherdataimporter.Debugger;
using Grassfish.UDC.PluginBase.Enums;
using Grassfish.UDC.PluginBase.V2.Interfaces;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace Weatherdataimporter.Debugger
{
    class Program
    {
        static void Main()
        {

            ILoggerFactory loggerFactory = LoggerFactory.Create(builder =>
            {

                builder.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Information);
            });
            Microsoft.Extensions.Logging.ILogger logger = (ILogger)loggerFactory.CreateLogger("Program");
            try
            {

                var parameters = new ImportParameters();

                // Dependency Injection setup
                var serviceCollection = DependencyInjection.CreateServiceCollection(new PluginLoggingDelegates(
                    (s, o) => Console.WriteLine(s),
                    (s, o) => Console.WriteLine(s),
                    (s, o) => Console.WriteLine(s),
                    (s, o) => Console.WriteLine(s)
                ));

                var plugin = new Plugin(serviceCollection.BuildServiceProvider());
                var result = plugin.Import(CancellationToken.None, parameters);
                var elements = result.GetElementsToStore();

                foreach (var element in elements)
                {
                    // Process each element (Logging for demonstration)
                    string weatherCondition = GetWeatherCondition(element.WeatherId);
                    Console.WriteLine($"Processing element.. {element.City} - TempMax: {element.TempMax}°C - TempMin: {element.TempMin}°C - Condition: {weatherCondition} - Wind: {element.WindSpeed} m/s - Date: {element.Date}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Critical Exception: " + ex.Message);
            }
        }
    }
    class ImportParameters : IImportParameters<ICustomer, IFeed, ILocationData>
    {
        public ICustomer Customer => null;
        public IFeed FeedToImport => new Feed();
        public string WorkingDirectory { get; } = string.Empty;
        public IEnumerable<ILocationData> LocationData => new List<ILocationData> { new LocationData() };
        IEnumerable<ILocationData> IImportParameters<ICustomer, IFeed, ILocationData>.LocationData => LocationData;
    }
    class Feed : IFeed
    {
        public Guid DefinitionGuid { get; } = Guid.NewGuid();
        public string Name { get; } = "Vienna"; // City name
        public FeedDefinitionType Type => FeedDefinitionType.Web;
        public FeedUpdateType UpdateType => FeedUpdateType.Full;
        public string Source => "https://api.met.no/weatherapi/locationforecast/2.0/compact"; // met.no API URL
        public string Username => "48.2082"; // Latitude for Vienna
        public string Password => "16.3738"; // Longitude for Vienna
        public string SpecificValue { get; } = string.Empty;
    }
    public sealed class LocationData : ILocationData
    {
        public int LocationId { get; set; }
        public string BoxId { get; set; }
        public string Name { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public string Country { get; set; }
        public string City { get; } = "asdasd";
        public string ZipCode { get; set; }
        public string Address { get; set; }
        public Guid? EditionGuid { get; set; }
        public IEnumerable<string> Tags { get; set; }
        public IReadOnlyDictionary<string, string> CustomValues { get; set; }
    }
}